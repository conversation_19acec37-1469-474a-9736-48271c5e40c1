import { Drawer } from "expo-router/drawer";
import React from "react";
import { Platform, I18nManager } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

import { useColorScheme } from "@/hooks/useColorScheme";
import { Colors } from "@/constants/Colors";
import { CustomDrawerContent } from "@/components/CustomDrawerContent";

export default function DrawerLayout() {
  const colorScheme = useColorScheme();

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Drawer
        drawerContent={CustomDrawerContent}
        screenOptions={{
          headerShown: false,
          drawerPosition: I18nManager.isRTL ? "right" : "left",
          drawerType: Platform.select({
            ios: "slide",
            android: "front",
            default: "front",
          }),
          drawerStyle: {
            backgroundColor: Colors[colorScheme ?? "light"].background,
            width: 280,
            // Ensure proper RTL layout direction
            direction: I18nManager.isRTL ? 'rtl' : 'ltr',
          },
          drawerActiveTintColor: Colors[colorScheme ?? "light"].tint,
          drawerInactiveTintColor:
            Colors[colorScheme ?? "light"].tabIconDefault,
          drawerLabelStyle: {
            fontFamily: "Vazirmatn",
            fontSize: 16, // Fixed font size - was too small at 1
            textAlign: I18nManager.isRTL ? "right" : "left",
            writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
          },
          // Enable swipe gestures from the correct edge
          swipeEnabled: true,
          swipeEdgeWidth: 50,
          // Optimize for RTL gesture handling
          gestureHandlerProps: {
            enableTrackpadTwoFingerGesture: true,
          },
        }}
      >
        {/* <Drawer.Screen name="ExamsList" /> */}
        <Drawer.Screen name="index" />
      </Drawer>
    </GestureHandlerRootView>
  );
}
